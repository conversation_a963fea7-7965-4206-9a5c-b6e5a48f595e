// 收藏功能修复脚本 - 在浏览器控制台中运行
// 使用方法：复制此代码到浏览器控制台并按回车执行

(function() {
    console.log('🚀 开始修复收藏功能...');
    
    // 1. 测试收藏API
    async function testFavoriteAPI() {
        console.log('📡 测试收藏API...');
        
        try {
            // 获取当前用户token
            const authData = localStorage.getItem('fileShareAuth');
            if (!authData) {
                console.error('❌ 未找到登录信息，请先登录');
                return false;
            }
            
            const auth = JSON.parse(authData);
            const token = auth.token;
            
            if (!token) {
                console.error('❌ 未找到token，请重新登录');
                return false;
            }
            
            console.log('✅ 找到token:', token.substring(0, 20) + '...');
            
            // 测试获取收藏列表
            const response = await fetch('/api/favorites', {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });
            
            console.log('📊 收藏API响应状态:', response.status);
            
            if (response.ok) {
                const data = await response.json();
                console.log('✅ 收藏API正常工作');
                console.log('📋 收藏数据:', data);
                return true;
            } else {
                const errorText = await response.text();
                console.error('❌ 收藏API错误:', response.status, errorText);
                return false;
            }
            
        } catch (error) {
            console.error('❌ 收藏API测试失败:', error);
            return false;
        }
    }
    
    // 2. 修复收藏按钮事件
    function fixFavoriteButtons() {
        console.log('🔧 修复收藏按钮事件...');
        
        const favoriteButtons = document.querySelectorAll('[data-action="favorite"]');
        console.log(`找到 ${favoriteButtons.length} 个收藏按钮`);
        
        favoriteButtons.forEach((btn, index) => {
            // 移除旧的事件监听器
            btn.replaceWith(btn.cloneNode(true));
            
            // 重新获取按钮引用
            const newBtn = document.querySelectorAll('[data-action="favorite"]')[index];
            
            // 添加新的事件监听器
            newBtn.addEventListener('click', async function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                const fileItem = this.closest('.file-item');
                if (!fileItem) {
                    console.error('❌ 未找到文件项');
                    return;
                }
                
                const fileId = fileItem.dataset.fileId;
                if (!fileId) {
                    console.error('❌ 未找到文件ID');
                    return;
                }
                
                console.log(`⭐ 切换收藏状态: 文件ID ${fileId}`);
                
                try {
                    // 获取token
                    const authData = localStorage.getItem('fileShareAuth');
                    const auth = JSON.parse(authData);
                    const token = auth.token;
                    
                    // 调用收藏API
                    const response = await fetch('/api/favorites/toggle', {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            file_id: parseInt(fileId)
                        })
                    });
                    
                    if (response.ok) {
                        const result = await response.json();
                        console.log('✅ 收藏操作成功:', result);
                        
                        // 更新按钮状态
                        const icon = this.querySelector('i');
                        if (result.is_favorited) {
                            icon.className = 'fas fa-star';
                            this.classList.add('favorited');
                            this.title = '取消收藏';
                            console.log(`⭐ 已收藏文件 ${fileId}`);
                        } else {
                            icon.className = 'far fa-star';
                            this.classList.remove('favorited');
                            this.title = '收藏';
                            console.log(`☆ 已取消收藏文件 ${fileId}`);
                        }
                        
                        // 显示提示消息
                        if (typeof Components !== 'undefined' && Components.Toast) {
                            Components.Toast.show(
                                result.is_favorited ? '已收藏' : '已取消收藏',
                                'success'
                            );
                        }
                        
                    } else {
                        const errorText = await response.text();
                        console.error('❌ 收藏操作失败:', response.status, errorText);
                        
                        if (typeof Components !== 'undefined' && Components.Toast) {
                            Components.Toast.show('收藏操作失败', 'error');
                        }
                    }
                    
                } catch (error) {
                    console.error('❌ 收藏操作异常:', error);
                    
                    if (typeof Components !== 'undefined' && Components.Toast) {
                        Components.Toast.show('收藏操作失败', 'error');
                    }
                }
            });
            
            console.log(`✅ 修复收藏按钮 ${index + 1}/${favoriteButtons.length}`);
        });
        
        console.log('✅ 收藏按钮事件修复完成');
    }
    
    // 3. 修复收藏夹菜单
    function fixFavoritesMenu() {
        console.log('🔧 修复收藏夹菜单...');
        
        const favoritesLink = document.querySelector('a[href="#favorites"], .nav-item[data-view="favorites"]');
        if (favoritesLink) {
            favoritesLink.addEventListener('click', async function(e) {
                e.preventDefault();
                
                console.log('📂 打开收藏夹...');
                
                if (window.fileManager && typeof window.fileManager.showFavorites === 'function') {
                    await window.fileManager.showFavorites();
                } else {
                    console.error('❌ 文件管理器或showFavorites方法不存在');
                }
            });
            
            console.log('✅ 收藏夹菜单修复完成');
        } else {
            console.warn('⚠️ 未找到收藏夹菜单');
        }
    }
    
    // 4. 执行修复
    async function runFix() {
        console.log('🔄 开始执行修复...');
        
        // 测试API
        const apiWorking = await testFavoriteAPI();
        if (!apiWorking) {
            console.error('❌ 收藏API不可用，请检查后端服务');
            return;
        }
        
        // 修复按钮
        fixFavoriteButtons();
        
        // 修复菜单
        fixFavoritesMenu();
        
        console.log('✅ 收藏功能修复完成！');
        console.log('💡 现在可以测试收藏功能了：');
        console.log('   1. 点击任意图片的星星图标进行收藏');
        console.log('   2. 点击左侧菜单的"收藏夹"查看收藏的文件');
    }
    
    // 延迟执行，确保页面加载完成
    setTimeout(runFix, 1000);
    
    // 导出测试函数
    window.testFavorites = {
        testAPI: testFavoriteAPI,
        fixButtons: fixFavoriteButtons,
        fixMenu: fixFavoritesMenu
    };
    
})();
