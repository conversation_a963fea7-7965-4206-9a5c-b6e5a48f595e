/**
 * 收藏功能修复脚本
 * 直接在浏览器控制台中运行此脚本来修复收藏功能
 */

(function() {
    'use strict';
    
    console.log('🔧 开始修复收藏功能...');
    
    // 检查必要组件
    if (!window.fileManager) {
        console.error('❌ fileManager 不存在');
        return;
    }
    
    if (!window.CONFIG) {
        console.error('❌ CONFIG 不存在');
        return;
    }
    
    console.log('✅ 基础组件检查通过');
    
    // 深度检查文件管理器方法
    const requiredMethods = ['favoriteFile', 'updateFavoriteButtonState', 'isFileFavorited', 'getFavorites', 'saveFavorites'];
    const missingMethods = requiredMethods.filter(method => typeof window.fileManager[method] !== 'function');
    
    if (missingMethods.length > 0) {
        console.error('❌ 缺少必要方法:', missingMethods);
        return;
    }
    
    console.log('✅ 文件管理器方法检查通过');
    
    // 强制刷新收藏状态
    function forceRefreshFavorites() {
        console.log('🔄 强制刷新收藏状态...');
        
        const favoriteButtons = document.querySelectorAll('[data-action="favorite"]');
        console.log(`找到 ${favoriteButtons.length} 个收藏按钮`);
        
        favoriteButtons.forEach((btn, index) => {
            const fileItem = btn.closest('.file-item');
            if (fileItem) {
                const fileId = fileItem.dataset.fileId;
                if (fileId) {
                    const favorites = JSON.parse(localStorage.getItem('fileShareFavorites') || '[]');
                    const isFavorited = favorites.some(fav => fav.id === fileId);
                    
                    const icon = btn.querySelector('i');
                    if (icon) {
                        if (isFavorited) {
                            icon.className = 'fas fa-star';
                            btn.classList.add('favorited');
                            btn.title = '取消收藏';
                        } else {
                            icon.className = 'far fa-star';
                            btn.classList.remove('favorited');
                            btn.title = '收藏';
                        }
                        console.log(`按钮 ${index + 1}: ${fileId} -> ${isFavorited}`);
                    }
                }
            }
        });
        
        console.log('✅ 收藏状态刷新完成');
    }
    
    // 修复事件绑定
    function fixEventBinding() {
        console.log('🔧 修复事件绑定...');
        
        // 移除旧的事件监听器
        if (window.favoriteClickHandler) {
            document.removeEventListener('click', window.favoriteClickHandler);
        }
        
        // 创建新的事件处理器
        window.favoriteClickHandler = function(e) {
            const actionBtn = e.target.closest('.action-btn');
            if (!actionBtn) return;
            
            const action = actionBtn.dataset.action;
            if (action !== 'favorite') return;
            
            e.preventDefault();
            e.stopPropagation();
            
            const fileItem = actionBtn.closest('.file-item');
            const fileId = fileItem?.dataset.fileId;
            
            console.log(`🌟 收藏按钮点击: ${fileId}`);
            
            if (!fileId) {
                console.error('❌ 未找到文件ID');
                return;
            }
            
            // 确保文件管理器存在并且有必要的方法
            if (window.fileManager && typeof window.fileManager.favoriteFile === 'function') {
                console.log(`📞 调用 favoriteFile(${fileId})`);
                
                // 异步调用收藏方法
                Promise.resolve(window.fileManager.favoriteFile(fileId))
                    .then(() => {
                        console.log(`✅ 收藏操作完成: ${fileId}`);
                        // 强制刷新按钮状态
                        setTimeout(() => forceRefreshFavorites(), 100);
                    })
                    .catch(error => {
                        console.error(`❌ 收藏操作失败: ${fileId}`, error);
                    });
            } else {
                console.error('❌ favoriteFile 方法不存在或不可用');
            }
        };
        
        // 绑定新的事件监听器
        document.addEventListener('click', window.favoriteClickHandler);
        console.log('✅ 事件绑定修复完成');
    }
    
    // 测试收藏功能
    function testFavoriteFunction() {
        console.log('🧪 测试收藏功能...');
        
        const favoriteButtons = document.querySelectorAll('[data-action="favorite"]');
        if (favoriteButtons.length === 0) {
            console.warn('⚠️ 没有找到收藏按钮');
            return;
        }
        
        const testBtn = favoriteButtons[0];
        const fileItem = testBtn.closest('.file-item');
        const fileId = fileItem?.dataset.fileId;
        
        if (!fileId) {
            console.error('❌ 测试按钮没有文件ID');
            return;
        }
        
        console.log(`测试文件ID: ${fileId}`);
        
        const clickEvent = new MouseEvent('click', {
            bubbles: true,
            cancelable: true,
            view: window
        });
        
        testBtn.dispatchEvent(clickEvent);
        console.log('✅ 测试点击已发送');
    }
    
    // 显示当前收藏列表
    function showCurrentFavorites() {
        console.log('📋 当前收藏列表:');
        
        try {
            const favorites = JSON.parse(localStorage.getItem('fileShareFavorites') || '[]');
            console.table(favorites);
            console.log(`总计: ${favorites.length} 个收藏项`);
        } catch (error) {
            console.error('❌ 读取收藏列表失败:', error);
        }
    }
    
    // 清空收藏列表
    function clearFavorites() {
        localStorage.removeItem('fileShareFavorites');
        console.log('🗑️ 收藏列表已清空');
        forceRefreshFavorites();
    }
    
    // 强制初始化文件管理器
    function forceInitFileManager() {
        console.log('🔧 强制初始化文件管理器...');

        if (window.fileManager && typeof window.fileManager.init === 'function') {
            try {
                window.fileManager.init();
                console.log('✅ 文件管理器重新初始化成功');
            } catch (error) {
                console.error('❌ 文件管理器初始化失败:', error);
            }
        } else {
            console.warn('⚠️ 文件管理器或init方法不存在');
        }
    }

    // 执行修复
    console.log('🚀 开始执行修复步骤...');
    forceInitFileManager();
    forceRefreshFavorites();
    fixEventBinding();
    showCurrentFavorites();
    
    // 导出工具函数
    window.favoriteTools = {
        refresh: forceRefreshFavorites,
        fixEvents: fixEventBinding,
        test: testFavoriteFunction,
        show: showCurrentFavorites,
        clear: clearFavorites,
        initManager: forceInitFileManager
    };
    
    console.log('✅ 收藏功能修复完成！');
    console.log('💡 可用命令:');
    console.log('  favoriteTools.refresh() - 刷新收藏状态');
    console.log('  favoriteTools.test() - 测试收藏功能');
    console.log('  favoriteTools.show() - 显示收藏列表');
    console.log('  favoriteTools.clear() - 清空收藏列表');
    console.log('  favoriteTools.initManager() - 重新初始化文件管理器');
    
})();
