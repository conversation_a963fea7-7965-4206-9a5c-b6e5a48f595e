<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>紧急收藏功能修复工具</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
            font-size: 28px;
        }
        
        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid;
        }
        
        .alert-danger {
            background-color: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }
        
        .alert-info {
            background-color: #d1ecf1;
            border-color: #17a2b8;
            color: #0c5460;
        }
        
        .alert-success {
            background-color: #d4edda;
            border-color: #28a745;
            color: #155724;
        }
        
        .btn {
            display: inline-block;
            padding: 12px 24px;
            margin: 10px 5px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #0056b3;
            transform: translateY(-2px);
        }
        
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        
        .btn-success:hover {
            background-color: #1e7e34;
            transform: translateY(-2px);
        }
        
        .btn-warning {
            background-color: #ffc107;
            color: #212529;
        }
        
        .btn-warning:hover {
            background-color: #e0a800;
            transform: translateY(-2px);
        }
        
        .code-block {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            white-space: pre-wrap;
            word-wrap: break-word;
        }
        
        .steps {
            counter-reset: step-counter;
        }
        
        .step {
            counter-increment: step-counter;
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        
        .step::before {
            content: "步骤 " counter(step-counter) ": ";
            font-weight: bold;
            color: #007bff;
        }
        
        .text-center {
            text-align: center;
        }
        
        .mt-3 {
            margin-top: 1rem;
        }
        
        .mb-3 {
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚨 紧急收藏功能修复工具</h1>
        
        <div class="alert alert-danger">
            <strong>问题描述：</strong> 收藏功能显示"文件不存在"错误，无法正常添加收藏。
        </div>
        
        <div class="alert alert-info">
            <strong>解决方案：</strong> 使用紧急修复脚本，绕过文件查找问题，直接从DOM获取文件信息。
        </div>
        
        <div class="steps">
            <div class="step">
                在主系统页面按 F12 打开开发者工具，切换到 Console 标签
            </div>
            
            <div class="step">
                点击下面的"复制修复脚本"按钮，将脚本复制到剪贴板
            </div>
            
            <div class="step">
                在 Console 中粘贴脚本并按回车执行
            </div>
            
            <div class="step">
                等待看到"✅ 紧急修复完成！"消息
            </div>
            
            <div class="step">
                测试收藏功能：点击任意图片的星星图标
            </div>
        </div>
        
        <div class="text-center mt-3">
            <button class="btn btn-primary" onclick="copyScript()">📋 复制修复脚本</button>
            <button class="btn btn-success" onclick="copyTestCommand()">🧪 复制测试命令</button>
            <button class="btn btn-warning" onclick="showScript()">👁️ 查看脚本内容</button>
        </div>
        
        <div id="script-content" class="code-block" style="display: none;"></div>
        
        <div class="alert alert-success mt-3">
            <strong>修复后可用命令：</strong><br>
            • <code>emergencyFavoriteTools.test()</code> - 测试收藏功能<br>
            • <code>emergencyFavoriteTools.refresh()</code> - 刷新收藏状态<br>
            • <code>emergencyFavoriteTools.rebind()</code> - 重新绑定事件<br>
            • <code>emergencyFavoriteTools.restore()</code> - 恢复原始方法
        </div>
    </div>

    <script>
        const emergencyScript = `/**
 * 紧急收藏功能修复脚本
 * 解决"文件不存在"问题
 */

(function() {
    'use strict';
    
    console.log('🚨 紧急修复收藏功能 - 解决文件不存在问题...');
    
    // 检查基础组件
    if (!window.fileManager) {
        console.error('❌ fileManager 不存在，无法修复');
        return;
    }
    
    // 备份原始方法
    const originalFavoriteFile = window.fileManager.favoriteFile;
    const originalUpdateFavoriteButtonState = window.fileManager.updateFavoriteButtonState;
    
    // 重写 favoriteFile 方法，绕过文件查找问题
    window.fileManager.favoriteFile = async function(fileId) {
        try {
            console.log(\`🌟 开始收藏操作: \${fileId}\`);
            
            if (!fileId) {
                console.error('❌ 文件ID为空');
                this.showToast('文件ID无效', 'error');
                return;
            }
            
            // 从DOM中获取文件信息，而不是从 this.files 数组
            const fileElement = document.querySelector(\`[data-file-id="\${fileId}"]\`);
            if (!fileElement) {
                console.error('❌ 未找到文件元素');
                this.showToast('文件元素不存在', 'error');
                return;
            }
            
            // 从DOM元素中提取文件信息
            const fileName = fileElement.querySelector('.file-name')?.textContent || \`文件_\${fileId}\`;
            const fileType = fileElement.dataset.fileType || 'file';
            const fileSizeElement = fileElement.querySelector('.file-size');
            const fileSize = fileSizeElement ? fileSizeElement.textContent : '未知大小';
            
            console.log(\`📁 从DOM获取文件信息: \${fileName}, 类型: \${fileType}\`);
            
            // 获取当前收藏状态
            const favorites = this.getFavorites();
            const isFavorited = favorites.some(fav => fav.id === fileId);
            
            console.log(\`💫 当前收藏状态: \${isFavorited}\`);
            
            if (isFavorited) {
                // 取消收藏
                const updatedFavorites = favorites.filter(fav => fav.id !== fileId);
                this.saveFavorites(updatedFavorites);
                this.showToast(\`已取消收藏 "\${fileName}"\`, 'warning');
                this.updateFavoriteButtonState(fileId, false);
                console.log(\`✅ 取消收藏成功: \${fileName}\`);
            } else {
                // 添加收藏
                const favoriteItem = {
                    id: fileId,
                    name: fileName,
                    type: fileType,
                    size: fileSize,
                    modified_at: new Date().toISOString(),
                    favorited_at: new Date().toISOString(),
                    folder_id: this.currentFolderId || null,
                    folder_name: this.currentFolder?.name || '根目录'
                };
                
                favorites.push(favoriteItem);
                this.saveFavorites(favorites);
                this.showToast(\`已收藏 "\${fileName}"\`, 'success');
                this.updateFavoriteButtonState(fileId, true);
                console.log(\`✅ 添加收藏成功: \${fileName}\`);
            }
            
        } catch (error) {
            console.error('❌ 收藏操作失败:', error);
            this.showToast('收藏操作失败', 'error');
        }
    };
    
    // 增强 updateFavoriteButtonState 方法
    window.fileManager.updateFavoriteButtonState = function(fileId, isFavorited) {
        console.log(\`🔄 更新收藏按钮状态: \${fileId} -> \${isFavorited}\`);
        
        // 查找所有相关的收藏按钮
        const selectors = [
            \`[data-file-id="\${fileId}"] [data-action="favorite"]\`,
            \`[data-file-id="\${fileId}"].file-item [data-action="favorite"]\`,
            \`.file-item[data-file-id="\${fileId}"] [data-action="favorite"]\`
        ];
        
        let buttonsFound = 0;
        
        selectors.forEach(selector => {
            const buttons = document.querySelectorAll(selector);
            buttons.forEach((btn, index) => {
                const icon = btn.querySelector('i');
                if (icon) {
                    if (isFavorited) {
                        icon.className = 'fas fa-star';
                        btn.classList.add('favorited');
                        btn.title = '取消收藏';
                    } else {
                        icon.className = 'far fa-star';
                        btn.classList.remove('favorited');
                        btn.title = '收藏';
                    }
                    buttonsFound++;
                    console.log(\`✅ 更新按钮 \${buttonsFound}: \${isFavorited ? '已收藏' : '未收藏'}\`);
                }
            });
        });
        
        // 同时更新预览模态框中的收藏按钮
        const previewModal = document.querySelector('#preview-modal');
        if (previewModal && previewModal.style.display !== 'none') {
            const previewBtn = previewModal.querySelector('[data-action="favorite"]');
            if (previewBtn) {
                const previewIcon = previewBtn.querySelector('i');
                if (previewIcon) {
                    if (isFavorited) {
                        previewIcon.className = 'fas fa-star';
                        previewBtn.title = '取消收藏';
                    } else {
                        previewIcon.className = 'far fa-star';
                        previewBtn.title = '收藏';
                    }
                    buttonsFound++;
                    console.log(\`✅ 更新预览按钮: \${isFavorited ? '已收藏' : '未收藏'}\`);
                }
            }
        }
        
        console.log(\`📊 总共更新了 \${buttonsFound} 个收藏按钮\`);
    };
    
    // 强制重新绑定所有收藏按钮事件
    function rebindFavoriteEvents() {
        console.log('🔗 重新绑定收藏按钮事件...');
        
        // 移除旧的事件监听器
        if (window.emergencyFavoriteHandler) {
            document.removeEventListener('click', window.emergencyFavoriteHandler);
        }
        
        // 创建新的事件处理器
        window.emergencyFavoriteHandler = function(e) {
            const favoriteBtn = e.target.closest('[data-action="favorite"]');
            if (!favoriteBtn) return;
            
            e.preventDefault();
            e.stopPropagation();
            
            const fileItem = favoriteBtn.closest('[data-file-id]');
            if (!fileItem) {
                console.error('❌ 未找到文件项');
                return;
            }
            
            const fileId = fileItem.dataset.fileId;
            console.log(\`🎯 收藏按钮点击: \${fileId}\`);
            
            if (window.fileManager && typeof window.fileManager.favoriteFile === 'function') {
                window.fileManager.favoriteFile(fileId);
            } else {
                console.error('❌ favoriteFile 方法不可用');
            }
        };
        
        // 绑定新的事件监听器
        document.addEventListener('click', window.emergencyFavoriteHandler);
        console.log('✅ 收藏按钮事件重新绑定完成');
    }
    
    // 刷新所有收藏状态
    function refreshAllFavoriteStates() {
        console.log('🔄 刷新所有收藏状态...');
        
        const favoriteButtons = document.querySelectorAll('[data-action="favorite"]');
        console.log(\`找到 \${favoriteButtons.length} 个收藏按钮\`);
        
        favoriteButtons.forEach((btn, index) => {
            const fileItem = btn.closest('[data-file-id]');
            if (fileItem) {
                const fileId = fileItem.dataset.fileId;
                if (fileId) {
                    const favorites = JSON.parse(localStorage.getItem('fileShareFavorites') || '[]');
                    const isFavorited = favorites.some(fav => fav.id === fileId);
                    window.fileManager.updateFavoriteButtonState(fileId, isFavorited);
                }
            }
        });
        
        console.log('✅ 收藏状态刷新完成');
    }
    
    // 测试收藏功能
    function testFavoriteFunction() {
        console.log('🧪 测试收藏功能...');
        
        const favoriteButtons = document.querySelectorAll('[data-action="favorite"]');
        if (favoriteButtons.length === 0) {
            console.warn('⚠️ 没有找到收藏按钮');
            return;
        }
        
        const testBtn = favoriteButtons[0];
        const fileItem = testBtn.closest('[data-file-id]');
        const fileId = fileItem?.dataset.fileId;
        
        if (!fileId) {
            console.error('❌ 测试按钮没有文件ID');
            return;
        }
        
        console.log(\`🎯 测试文件ID: \${fileId}\`);
        
        // 模拟点击
        testBtn.click();
        console.log('✅ 测试点击已发送');
    }
    
    // 执行修复
    console.log('🚀 开始执行紧急修复...');
    rebindFavoriteEvents();
    refreshAllFavoriteStates();
    
    // 导出工具函数
    window.emergencyFavoriteTools = {
        rebind: rebindFavoriteEvents,
        refresh: refreshAllFavoriteStates,
        test: testFavoriteFunction,
        restore: () => {
            window.fileManager.favoriteFile = originalFavoriteFile;
            window.fileManager.updateFavoriteButtonState = originalUpdateFavoriteButtonState;
            console.log('🔄 已恢复原始方法');
        }
    };
    
    console.log('✅ 紧急修复完成！');
    console.log('💡 可用命令:');
    console.log('  emergencyFavoriteTools.test() - 测试收藏功能');
    console.log('  emergencyFavoriteTools.refresh() - 刷新收藏状态');
    console.log('  emergencyFavoriteTools.rebind() - 重新绑定事件');
    console.log('  emergencyFavoriteTools.restore() - 恢复原始方法');
    
})();`;

        function copyScript() {
            navigator.clipboard.writeText(emergencyScript).then(() => {
                alert('✅ 修复脚本已复制到剪贴板！\n\n请在主系统页面的控制台中粘贴并执行。');
            }).catch(err => {
                console.error('复制失败:', err);
                showScript();
                alert('❌ 自动复制失败，请手动复制下面显示的脚本内容。');
            });
        }

        function copyTestCommand() {
            const testCommand = 'emergencyFavoriteTools.test()';
            navigator.clipboard.writeText(testCommand).then(() => {
                alert('✅ 测试命令已复制到剪贴板！\n\n在执行修复脚本后，可以使用此命令测试收藏功能。');
            }).catch(err => {
                console.error('复制失败:', err);
                alert('❌ 复制失败，请手动输入: emergencyFavoriteTools.test()');
            });
        }

        function showScript() {
            const scriptContent = document.getElementById('script-content');
            if (scriptContent.style.display === 'none') {
                scriptContent.textContent = emergencyScript;
                scriptContent.style.display = 'block';
            } else {
                scriptContent.style.display = 'none';
            }
        }
    </script>
</body>
</html>
