/**
 * 收藏功能修复脚本
 * 在浏览器控制台中运行: copy(fixFavoriteScript); 然后粘贴到控制台执行
 */

const fixFavoriteScript = `
(function() {
    'use strict';
    
    console.log('🔧 开始修复收藏功能...');
    
    // 1. 检查必要组件
    if (!window.fileManager) {
        console.error('❌ fileManager 不存在');
        return;
    }
    
    if (!window.CONFIG) {
        console.error('❌ CONFIG 不存在');
        return;
    }
    
    console.log('✅ 基础组件检查通过');
    
    // 2. 强制刷新收藏状态
    function forceRefreshFavorites() {
        console.log('🔄 强制刷新收藏状态...');
        
        const favoriteButtons = document.querySelectorAll('[data-action="favorite"]');
        console.log(\`找到 \${favoriteButtons.length} 个收藏按钮\`);
        
        favoriteButtons.forEach((btn, index) => {
            const fileItem = btn.closest('.file-item');
            if (fileItem) {
                const fileId = fileItem.dataset.fileId;
                if (fileId) {
                    // 获取收藏状态
                    const favorites = JSON.parse(localStorage.getItem('fileShareFavorites') || '[]');
                    const isFavorited = favorites.some(fav => fav.id === fileId);
                    
                    // 更新按钮状态
                    const icon = btn.querySelector('i');
                    if (icon) {
                        if (isFavorited) {
                            icon.className = 'fas fa-star';
                            btn.classList.add('favorited');
                            btn.title = '取消收藏';
                        } else {
                            icon.className = 'far fa-star';
                            btn.classList.remove('favorited');
                            btn.title = '收藏';
                        }
                        console.log(\`按钮 \${index + 1}: \${fileId} -> \${isFavorited}\`);
                    }
                }
            }
        });
        
        console.log('✅ 收藏状态刷新完成');
    }
    
    // 3. 修复事件绑定
    function fixEventBinding() {
        console.log('🔧 修复事件绑定...');
        
        // 移除可能的重复事件监听器
        document.removeEventListener('click', window.favoriteClickHandler);
        
        // 创建新的事件处理器
        window.favoriteClickHandler = function(e) {
            const actionBtn = e.target.closest('.action-btn');
            if (!actionBtn) return;
            
            const action = actionBtn.dataset.action;
            if (action !== 'favorite') return;
            
            e.preventDefault();
            e.stopPropagation();
            
            const fileItem = actionBtn.closest('.file-item');
            const fileId = fileItem?.dataset.fileId;
            
            console.log(\`收藏按钮点击: \${fileId}\`);
            
            if (!fileId) {
                console.error('未找到文件ID');
                return;
            }
            
            // 执行收藏操作
            if (window.fileManager && typeof window.fileManager.favoriteFile === 'function') {
                window.fileManager.favoriteFile(fileId);
            } else {
                console.error('favoriteFile 方法不存在');
            }
        };
        
        // 绑定新的事件监听器
        document.addEventListener('click', window.favoriteClickHandler);
        
        console.log('✅ 事件绑定修复完成');
    }
    
    // 4. 测试收藏功能
    function testFavoriteFunction() {
        console.log('🧪 测试收藏功能...');
        
        const favoriteButtons = document.querySelectorAll('[data-action="favorite"]');
        if (favoriteButtons.length === 0) {
            console.warn('⚠️ 没有找到收藏按钮');
            return;
        }
        
        const testBtn = favoriteButtons[0];
        const fileItem = testBtn.closest('.file-item');
        const fileId = fileItem?.dataset.fileId;
        
        if (!fileId) {
            console.error('❌ 测试按钮没有文件ID');
            return;
        }
        
        console.log(\`测试文件ID: \${fileId}\`);
        
        // 模拟点击
        const clickEvent = new MouseEvent('click', {
            bubbles: true,
            cancelable: true,
            view: window
        });
        
        testBtn.dispatchEvent(clickEvent);
        console.log('✅ 测试点击已发送');
    }
    
    // 5. 显示当前收藏列表
    function showCurrentFavorites() {
        console.log('📋 当前收藏列表:');
        
        try {
            const favorites = JSON.parse(localStorage.getItem('fileShareFavorites') || '[]');
            console.table(favorites);
            console.log(\`总计: \${favorites.length} 个收藏项\`);
        } catch (error) {
            console.error('❌ 读取收藏列表失败:', error);
        }
    }
    
    // 6. 清空收藏列表（用于测试）
    function clearFavorites() {
        localStorage.removeItem('fileShareFavorites');
        console.log('🗑️ 收藏列表已清空');
        forceRefreshFavorites();
    }
    
    // 执行修复
    console.log('🚀 开始执行修复步骤...');
    
    // 步骤1: 刷新收藏状态
    forceRefreshFavorites();
    
    // 步骤2: 修复事件绑定
    fixEventBinding();
    
    // 步骤3: 显示当前状态
    showCurrentFavorites();
    
    // 导出工具函数
    window.favoriteTools = {
        refresh: forceRefreshFavorites,
        fixEvents: fixEventBinding,
        test: testFavoriteFunction,
        show: showCurrentFavorites,
        clear: clearFavorites
    };
    
    console.log('✅ 收藏功能修复完成！');
    console.log('💡 可用命令:');
    console.log('  favoriteTools.refresh() - 刷新收藏状态');
    console.log('  favoriteTools.test() - 测试收藏功能');
    console.log('  favoriteTools.show() - 显示收藏列表');
    console.log('  favoriteTools.clear() - 清空收藏列表');
    
})();
`;

// 如果在浏览器环境中直接运行
if (typeof window !== 'undefined') {
    eval(fixFavoriteScript);
}
