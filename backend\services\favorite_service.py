#!/usr/bin/env python3
"""
收藏功能服务
"""

import logging
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, asc, func

from models.favorite import UserFavorite, FavoriteFolder, FavoriteFolderItem
from models.file_share import SharedFile
from models.user import User
from services.database_manager import DatabaseManager


class FavoriteService:
    """收藏功能服务"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager
        self.logger = logging.getLogger(__name__)
    
    def add_favorite(self, user_id: int, file_id: int, notes: str = None) -> Dict[str, Any]:
        """添加收藏"""
        try:
            with self.db_manager.get_session() as session:
                # 检查文件是否存在
                file_record = session.query(SharedFile).filter_by(id=file_id).first()
                if not file_record:
                    return {"success": False, "error": "文件不存在"}
                
                # 检查是否已经收藏
                existing = session.query(UserFavorite).filter_by(
                    user_id=user_id, 
                    file_id=file_id,
                    is_active=True
                ).first()
                
                if existing:
                    return {"success": False, "error": "文件已收藏"}
                
                # 创建收藏记录
                favorite = UserFavorite(
                    user_id=user_id,
                    file_id=file_id,
                    notes=notes
                )
                
                session.add(favorite)
                session.commit()
                
                self.logger.info(f"用户 {user_id} 收藏文件 {file_id}")
                
                return {
                    "success": True,
                    "data": favorite.to_dict()
                }
                
        except Exception as e:
            self.logger.error(f"添加收藏失败: {e}")
            return {"success": False, "error": str(e)}
    
    def remove_favorite(self, user_id: int, file_id: int) -> Dict[str, Any]:
        """取消收藏"""
        try:
            with self.db_manager.get_session() as session:
                favorite = session.query(UserFavorite).filter_by(
                    user_id=user_id,
                    file_id=file_id,
                    is_active=True
                ).first()
                
                if not favorite:
                    return {"success": False, "error": "收藏记录不存在"}
                
                # 软删除：标记为无效
                favorite.is_active = False
                favorite.updated_at = datetime.now()
                
                session.commit()
                
                self.logger.info(f"用户 {user_id} 取消收藏文件 {file_id}")
                
                return {"success": True}
                
        except Exception as e:
            self.logger.error(f"取消收藏失败: {e}")
            return {"success": False, "error": str(e)}
    
    def toggle_favorite(self, user_id: int, file_id: int, notes: str = None) -> Dict[str, Any]:
        """切换收藏状态"""
        try:
            with self.db_manager.get_session() as session:
                # 检查当前收藏状态
                favorite = session.query(UserFavorite).filter_by(
                    user_id=user_id,
                    file_id=file_id,
                    is_active=True
                ).first()
                
                if favorite:
                    # 已收藏，取消收藏
                    favorite.is_active = False
                    favorite.updated_at = datetime.now()
                    session.commit()
                    
                    self.logger.info(f"用户 {user_id} 取消收藏文件 {file_id}")
                    return {
                        "success": True,
                        "action": "removed",
                        "is_favorited": False
                    }
                else:
                    # 未收藏，添加收藏
                    # 检查文件是否存在
                    file_record = session.query(SharedFile).filter_by(id=file_id).first()
                    if not file_record:
                        return {"success": False, "error": "文件不存在"}
                    
                    # 检查是否有历史收藏记录（可能被软删除）
                    old_favorite = session.query(UserFavorite).filter_by(
                        user_id=user_id,
                        file_id=file_id
                    ).first()
                    
                    if old_favorite:
                        # 恢复历史记录
                        old_favorite.is_active = True
                        old_favorite.favorited_at = datetime.now()
                        old_favorite.updated_at = datetime.now()
                        if notes:
                            old_favorite.notes = notes
                        favorite_data = old_favorite.to_dict()
                    else:
                        # 创建新记录
                        new_favorite = UserFavorite(
                            user_id=user_id,
                            file_id=file_id,
                            notes=notes
                        )
                        session.add(new_favorite)
                        session.flush()  # 获取ID
                        favorite_data = new_favorite.to_dict()
                    
                    session.commit()
                    
                    self.logger.info(f"用户 {user_id} 收藏文件 {file_id}")
                    return {
                        "success": True,
                        "action": "added",
                        "is_favorited": True,
                        "data": favorite_data
                    }
                    
        except Exception as e:
            self.logger.error(f"切换收藏状态失败: {e}")
            return {"success": False, "error": str(e)}
    
    def get_user_favorites(self, user_id: int, page: int = 1, page_size: int = 50, 
                          folder_id: int = None) -> Dict[str, Any]:
        """获取用户收藏列表"""
        try:
            with self.db_manager.get_session() as session:
                # 构建查询
                query = session.query(UserFavorite).filter(
                    UserFavorite.user_id == user_id,
                    UserFavorite.is_active == True
                ).join(SharedFile).filter(
                    SharedFile.is_image == True  # 只返回图片文件
                )
                
                # 如果指定了文件夹，添加文件夹过滤
                if folder_id:
                    query = query.filter(SharedFile.folder_id == folder_id)
                
                # 按收藏时间倒序排列
                query = query.order_by(desc(UserFavorite.favorited_at))
                
                # 计算总数
                total_count = query.count()
                
                # 分页
                offset = (page - 1) * page_size
                favorites = query.offset(offset).limit(page_size).all()
                
                # 转换为字典
                favorite_list = [fav.to_dict() for fav in favorites]
                
                return {
                    "success": True,
                    "data": {
                        "favorites": favorite_list,
                        "total_count": total_count,
                        "page": page,
                        "page_size": page_size,
                        "total_pages": (total_count + page_size - 1) // page_size
                    }
                }
                
        except Exception as e:
            self.logger.error(f"获取用户收藏列表失败: {e}")
            return {"success": False, "error": str(e)}
    
    def check_favorite_status(self, user_id: int, file_ids: List[int]) -> Dict[str, Any]:
        """批量检查文件收藏状态"""
        try:
            with self.db_manager.get_session() as session:
                favorites = session.query(UserFavorite).filter(
                    UserFavorite.user_id == user_id,
                    UserFavorite.file_id.in_(file_ids),
                    UserFavorite.is_active == True
                ).all()
                
                # 构建状态字典
                status_dict = {}
                for file_id in file_ids:
                    status_dict[file_id] = False
                
                for favorite in favorites:
                    status_dict[favorite.file_id] = True
                
                return {
                    "success": True,
                    "data": status_dict
                }
                
        except Exception as e:
            self.logger.error(f"检查收藏状态失败: {e}")
            return {"success": False, "error": str(e)}
    
    def get_favorite_statistics(self, user_id: int) -> Dict[str, Any]:
        """获取收藏统计信息"""
        try:
            with self.db_manager.get_session() as session:
                # 总收藏数
                total_count = session.query(UserFavorite).filter(
                    UserFavorite.user_id == user_id,
                    UserFavorite.is_active == True
                ).count()
                
                # 按文件夹分组统计
                folder_stats = session.query(
                    SharedFile.folder_id,
                    func.count(UserFavorite.id).label('count')
                ).join(UserFavorite).filter(
                    UserFavorite.user_id == user_id,
                    UserFavorite.is_active == True
                ).group_by(SharedFile.folder_id).all()
                
                # 最近收藏（最近7天）
                from datetime import datetime, timedelta
                recent_date = datetime.now() - timedelta(days=7)
                recent_count = session.query(UserFavorite).filter(
                    UserFavorite.user_id == user_id,
                    UserFavorite.is_active == True,
                    UserFavorite.favorited_at >= recent_date
                ).count()
                
                return {
                    "success": True,
                    "data": {
                        "total_count": total_count,
                        "recent_count": recent_count,
                        "folder_stats": [
                            {"folder_id": stat.folder_id, "count": stat.count}
                            for stat in folder_stats
                        ]
                    }
                }
                
        except Exception as e:
            self.logger.error(f"获取收藏统计失败: {e}")
            return {"success": False, "error": str(e)}
