<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>收藏功能调试 - 文件共享系统</title>
    <link rel="stylesheet" href="css/variables.css">
    <link rel="stylesheet" href="css/base.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .debug-panel {
            position: fixed;
            top: 10px;
            right: 10px;
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10000;
            max-width: 300px;
            font-size: 12px;
        }
        .debug-log {
            max-height: 200px;
            overflow-y: auto;
            background: #f8f9fa;
            padding: 8px;
            border-radius: 4px;
            margin-top: 10px;
            font-family: monospace;
        }
        .debug-log div {
            margin-bottom: 2px;
            padding: 2px 4px;
            border-radius: 2px;
        }
        .debug-log .info { background: #d1ecf1; }
        .debug-log .success { background: #d4edda; }
        .debug-log .warning { background: #fff3cd; }
        .debug-log .error { background: #f8d7da; }
        .favorites-display {
            margin-top: 10px;
            padding: 8px;
            background: #e9ecef;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>收藏功能调试测试</h1>
        
        <div class="debug-panel">
            <h4>调试面板</h4>
            <button onclick="clearFavorites()">清空收藏</button>
            <button onclick="showFavorites()">显示收藏</button>
            <button onclick="clearLog()">清空日志</button>
            
            <div class="favorites-display" id="favorites-display">
                收藏数量: <span id="favorites-count">0</span>
            </div>
            
            <div class="debug-log" id="debug-log"></div>
        </div>
        
        <div class="file-grid" id="demo-grid">
            <!-- 测试图片文件1 -->
            <div class="file-item" data-file-id="test-101" data-file-type="file">
                <div class="file-icon">
                    <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjBmOGZmIi8+CiAgPHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzMzNzNkYyIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuekuuS+i+WbvueJhzE8L3RleHQ+Cjwvc3ZnPg==" alt="测试图片1" />
                </div>
                <div class="file-name" title="test-image-1.jpg">test-image-1.jpg</div>
                <div class="file-meta">
                    <span class="file-size">31.46 KB</span>
                </div>
                <div class="file-actions">
                    <button class="action-btn" data-action="download" title="下载">
                        <i class="fas fa-download"></i>
                    </button>
                    <button class="action-btn" data-action="preview" title="预览">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="action-btn" data-action="favorite" title="收藏">
                        <i class="far fa-star"></i>
                    </button>
                </div>
            </div>
            
            <!-- 测试图片文件2 -->
            <div class="file-item" data-file-id="test-102" data-file-type="file">
                <div class="file-icon">
                    <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjVmNWY1Ii8+CiAgPHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzY2NiIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuekuuS+i+WbvueJhzI8L3RleHQ+Cjwvc3ZnPg==" alt="测试图片2" />
                </div>
                <div class="file-name" title="test-image-2.psd">test-image-2.psd</div>
                <div class="file-meta">
                    <span class="file-size">2.3 MB</span>
                </div>
                <div class="file-actions">
                    <button class="action-btn" data-action="download" title="下载">
                        <i class="fas fa-download"></i>
                    </button>
                    <button class="action-btn" data-action="preview" title="预览">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="action-btn favorited" data-action="favorite" title="取消收藏">
                        <i class="fas fa-star"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 调试日志功能
        function debugLog(message, type = 'info') {
            const logContainer = document.getElementById('debug-log');
            const logEntry = document.createElement('div');
            logEntry.className = type;
            logEntry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
            console.log(`[DEBUG ${type.toUpperCase()}]`, message);
        }
        
        function clearLog() {
            document.getElementById('debug-log').innerHTML = '';
        }
        
        // 收藏功能实现
        function getFavorites() {
            try {
                const favoritesData = localStorage.getItem('fileShareFavorites');
                const favorites = favoritesData ? JSON.parse(favoritesData) : [];
                debugLog(`获取收藏列表: ${favorites.length} 个项目`, 'info');
                return favorites;
            } catch (error) {
                debugLog(`获取收藏列表失败: ${error.message}`, 'error');
                return [];
            }
        }
        
        function saveFavorites(favorites) {
            try {
                localStorage.setItem('fileShareFavorites', JSON.stringify(favorites));
                debugLog(`保存收藏列表: ${favorites.length} 个项目`, 'success');
                updateFavoritesDisplay();
            } catch (error) {
                debugLog(`保存收藏列表失败: ${error.message}`, 'error');
            }
        }
        
        function isFileFavorited(fileId) {
            const favorites = getFavorites();
            return favorites.some(fav => fav.id === fileId);
        }
        
        function favoriteFile(fileId) {
            try {
                debugLog(`开始处理收藏操作: ${fileId}`, 'info');
                
                // 模拟文件数据
                const fileElement = document.querySelector(`[data-file-id="${fileId}"]`);
                const fileName = fileElement.querySelector('.file-name').textContent;
                
                const file = {
                    id: fileId,
                    name: fileName,
                    type: 'file',
                    size: 1024 * 31, // 模拟文件大小
                    modified_at: new Date().toISOString()
                };
                
                const favorites = getFavorites();
                const isFavorited = favorites.some(fav => fav.id === fileId);
                
                if (isFavorited) {
                    // 取消收藏
                    const updatedFavorites = favorites.filter(fav => fav.id !== fileId);
                    saveFavorites(updatedFavorites);
                    debugLog(`取消收藏: ${file.name}`, 'warning');
                    updateFavoriteButtonState(fileId, false);
                    showToast(`已取消收藏 "${file.name}"`, 'warning');
                } else {
                    // 添加收藏
                    const favoriteItem = {
                        id: fileId,
                        name: file.name,
                        type: file.type,
                        size: file.size,
                        modified_at: file.modified_at,
                        favorited_at: new Date().toISOString(),
                        folder_id: null,
                        folder_name: '测试文件夹'
                    };
                    
                    favorites.push(favoriteItem);
                    saveFavorites(favorites);
                    debugLog(`添加收藏: ${file.name}`, 'success');
                    updateFavoriteButtonState(fileId, true);
                    showToast(`已收藏 "${file.name}"`, 'success');
                }
                
            } catch (error) {
                debugLog(`收藏操作失败: ${error.message}`, 'error');
                showToast('收藏操作失败', 'error');
            }
        }
        
        function updateFavoriteButtonState(fileId, isFavorited) {
            const fileItems = document.querySelectorAll(`[data-file-id="${fileId}"]`);
            fileItems.forEach(item => {
                const favoriteBtn = item.querySelector('[data-action="favorite"]');
                if (favoriteBtn) {
                    const icon = favoriteBtn.querySelector('i');
                    if (icon) {
                        if (isFavorited) {
                            icon.className = 'fas fa-star'; // 实心星星
                            favoriteBtn.classList.add('favorited');
                            favoriteBtn.title = '取消收藏';
                        } else {
                            icon.className = 'far fa-star'; // 空心星星
                            favoriteBtn.classList.remove('favorited');
                            favoriteBtn.title = '收藏';
                        }
                    }
                }
            });
            debugLog(`更新收藏按钮状态: ${fileId} -> ${isFavorited}`, 'info');
        }
        
        function updateFavoritesDisplay() {
            const favorites = getFavorites();
            document.getElementById('favorites-count').textContent = favorites.length;
        }
        
        function clearFavorites() {
            localStorage.removeItem('fileShareFavorites');
            debugLog('清空所有收藏', 'warning');
            updateFavoritesDisplay();
            // 更新所有按钮状态
            document.querySelectorAll('[data-action="favorite"]').forEach(btn => {
                const fileId = btn.closest('.file-item').dataset.fileId;
                updateFavoriteButtonState(fileId, false);
            });
        }
        
        function showFavorites() {
            const favorites = getFavorites();
            debugLog(`当前收藏列表: ${JSON.stringify(favorites, null, 2)}`, 'info');
        }
        
        // 简单的提示消息功能
        function showToast(message, type = 'info') {
            const existingToast = document.querySelector('.demo-toast');
            if (existingToast) {
                existingToast.remove();
            }
            
            const toast = document.createElement('div');
            toast.className = `demo-toast demo-toast-${type}`;
            toast.textContent = message;
            toast.style.cssText = `
                position: fixed;
                top: 60px;
                right: 20px;
                background: ${type === 'success' ? '#d4edda' : type === 'warning' ? '#fff3cd' : type === 'error' ? '#f8d7da' : '#d1ecf1'};
                color: ${type === 'success' ? '#155724' : type === 'warning' ? '#856404' : type === 'error' ? '#721c24' : '#0c5460'};
                border: 1px solid ${type === 'success' ? '#c3e6cb' : type === 'warning' ? '#ffeaa7' : type === 'error' ? '#f5c6cb' : '#bee5eb'};
                border-radius: 8px;
                padding: 12px 16px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                z-index: 10001;
                font-size: 14px;
                max-width: 300px;
                animation: slideIn 0.3s ease;
            `;
            
            document.body.appendChild(toast);
            
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.style.animation = 'slideOut 0.3s ease';
                    setTimeout(() => toast.remove(), 300);
                }
            }, 3000);
        }
        
        // 事件绑定
        document.addEventListener('DOMContentLoaded', function() {
            debugLog('页面加载完成，开始初始化', 'info');
            
            // 初始化收藏状态
            document.querySelectorAll('[data-action="favorite"]').forEach(btn => {
                const fileId = btn.closest('.file-item').dataset.fileId;
                const isFavorited = isFileFavorited(fileId);
                updateFavoriteButtonState(fileId, isFavorited);
            });
            
            updateFavoritesDisplay();
            
            // 绑定点击事件
            document.addEventListener('click', function(e) {
                const actionBtn = e.target.closest('.action-btn');
                if (!actionBtn) return;
                
                e.preventDefault();
                e.stopPropagation();
                
                const action = actionBtn.dataset.action;
                const fileItem = actionBtn.closest('.file-item');
                const fileId = fileItem?.dataset.fileId;
                
                debugLog(`点击操作按钮: ${action}, 文件ID: ${fileId}`, 'info');
                
                if (!fileId) {
                    debugLog('未找到文件ID', 'error');
                    return;
                }
                
                switch (action) {
                    case 'favorite':
                        favoriteFile(fileId);
                        break;
                    case 'preview':
                        debugLog(`预览文件: ${fileId}`, 'info');
                        showToast('预览功能演示', 'info');
                        break;
                    case 'download':
                        debugLog(`下载文件: ${fileId}`, 'info');
                        showToast('下载功能演示', 'info');
                        break;
                    default:
                        debugLog(`未知操作: ${action}`, 'warning');
                }
            });
            
            debugLog('事件绑定完成', 'success');
        });
        
        // 添加动画样式
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
