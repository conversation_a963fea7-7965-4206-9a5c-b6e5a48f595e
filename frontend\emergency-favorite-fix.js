/**
 * 紧急收藏功能修复脚本
 * 解决"文件不存在"问题
 */

(function() {
    'use strict';
    
    console.log('🚨 紧急修复收藏功能 - 解决文件不存在问题...');
    
    // 检查基础组件
    if (!window.fileManager) {
        console.error('❌ fileManager 不存在，无法修复');
        return;
    }
    
    // 备份原始方法
    const originalFavoriteFile = window.fileManager.favoriteFile;
    const originalUpdateFavoriteButtonState = window.fileManager.updateFavoriteButtonState;
    
    // 重写 favoriteFile 方法，绕过文件查找问题
    window.fileManager.favoriteFile = async function(fileId) {
        try {
            console.log(`🌟 开始收藏操作: ${fileId}`);
            
            if (!fileId) {
                console.error('❌ 文件ID为空');
                this.showToast('文件ID无效', 'error');
                return;
            }
            
            // 从DOM中获取文件信息，而不是从 this.files 数组
            const fileElement = document.querySelector(`[data-file-id="${fileId}"]`);
            if (!fileElement) {
                console.error('❌ 未找到文件元素');
                this.showToast('文件元素不存在', 'error');
                return;
            }
            
            // 从DOM元素中提取文件信息
            const fileName = fileElement.querySelector('.file-name')?.textContent || `文件_${fileId}`;
            const fileType = fileElement.dataset.fileType || 'file';
            const fileSizeElement = fileElement.querySelector('.file-size');
            const fileSize = fileSizeElement ? fileSizeElement.textContent : '未知大小';
            
            console.log(`📁 从DOM获取文件信息: ${fileName}, 类型: ${fileType}`);
            
            // 获取当前收藏状态
            const favorites = this.getFavorites();
            const isFavorited = favorites.some(fav => fav.id === fileId);
            
            console.log(`💫 当前收藏状态: ${isFavorited}`);
            
            if (isFavorited) {
                // 取消收藏
                const updatedFavorites = favorites.filter(fav => fav.id !== fileId);
                this.saveFavorites(updatedFavorites);
                this.showToast(`已取消收藏 "${fileName}"`, 'warning');
                this.updateFavoriteButtonState(fileId, false);
                console.log(`✅ 取消收藏成功: ${fileName}`);
            } else {
                // 添加收藏
                const favoriteItem = {
                    id: fileId,
                    name: fileName,
                    type: fileType,
                    size: fileSize,
                    modified_at: new Date().toISOString(),
                    favorited_at: new Date().toISOString(),
                    folder_id: this.currentFolderId || null,
                    folder_name: this.currentFolder?.name || '根目录'
                };
                
                favorites.push(favoriteItem);
                this.saveFavorites(favorites);
                this.showToast(`已收藏 "${fileName}"`, 'success');
                this.updateFavoriteButtonState(fileId, true);
                console.log(`✅ 添加收藏成功: ${fileName}`);
            }
            
        } catch (error) {
            console.error('❌ 收藏操作失败:', error);
            this.showToast('收藏操作失败', 'error');
        }
    };
    
    // 增强 updateFavoriteButtonState 方法
    window.fileManager.updateFavoriteButtonState = function(fileId, isFavorited) {
        console.log(`🔄 更新收藏按钮状态: ${fileId} -> ${isFavorited}`);
        
        // 查找所有相关的收藏按钮
        const selectors = [
            `[data-file-id="${fileId}"] [data-action="favorite"]`,
            `[data-file-id="${fileId}"].file-item [data-action="favorite"]`,
            `.file-item[data-file-id="${fileId}"] [data-action="favorite"]`
        ];
        
        let buttonsFound = 0;
        
        selectors.forEach(selector => {
            const buttons = document.querySelectorAll(selector);
            buttons.forEach((btn, index) => {
                const icon = btn.querySelector('i');
                if (icon) {
                    if (isFavorited) {
                        icon.className = 'fas fa-star';
                        btn.classList.add('favorited');
                        btn.title = '取消收藏';
                    } else {
                        icon.className = 'far fa-star';
                        btn.classList.remove('favorited');
                        btn.title = '收藏';
                    }
                    buttonsFound++;
                    console.log(`✅ 更新按钮 ${buttonsFound}: ${isFavorited ? '已收藏' : '未收藏'}`);
                }
            });
        });
        
        // 同时更新预览模态框中的收藏按钮
        const previewModal = document.querySelector('#preview-modal');
        if (previewModal && previewModal.style.display !== 'none') {
            const previewBtn = previewModal.querySelector('[data-action="favorite"]');
            if (previewBtn) {
                const previewIcon = previewBtn.querySelector('i');
                if (previewIcon) {
                    if (isFavorited) {
                        previewIcon.className = 'fas fa-star';
                        previewBtn.title = '取消收藏';
                    } else {
                        previewIcon.className = 'far fa-star';
                        previewBtn.title = '收藏';
                    }
                    buttonsFound++;
                    console.log(`✅ 更新预览按钮: ${isFavorited ? '已收藏' : '未收藏'}`);
                }
            }
        }
        
        console.log(`📊 总共更新了 ${buttonsFound} 个收藏按钮`);
    };
    
    // 强制重新绑定所有收藏按钮事件
    function rebindFavoriteEvents() {
        console.log('🔗 重新绑定收藏按钮事件...');
        
        // 移除旧的事件监听器
        if (window.emergencyFavoriteHandler) {
            document.removeEventListener('click', window.emergencyFavoriteHandler);
        }
        
        // 创建新的事件处理器
        window.emergencyFavoriteHandler = function(e) {
            const favoriteBtn = e.target.closest('[data-action="favorite"]');
            if (!favoriteBtn) return;
            
            e.preventDefault();
            e.stopPropagation();
            
            const fileItem = favoriteBtn.closest('[data-file-id]');
            if (!fileItem) {
                console.error('❌ 未找到文件项');
                return;
            }
            
            const fileId = fileItem.dataset.fileId;
            console.log(`🎯 收藏按钮点击: ${fileId}`);
            
            if (window.fileManager && typeof window.fileManager.favoriteFile === 'function') {
                window.fileManager.favoriteFile(fileId);
            } else {
                console.error('❌ favoriteFile 方法不可用');
            }
        };
        
        // 绑定新的事件监听器
        document.addEventListener('click', window.emergencyFavoriteHandler);
        console.log('✅ 收藏按钮事件重新绑定完成');
    }
    
    // 刷新所有收藏状态
    function refreshAllFavoriteStates() {
        console.log('🔄 刷新所有收藏状态...');
        
        const favoriteButtons = document.querySelectorAll('[data-action="favorite"]');
        console.log(`找到 ${favoriteButtons.length} 个收藏按钮`);
        
        favoriteButtons.forEach((btn, index) => {
            const fileItem = btn.closest('[data-file-id]');
            if (fileItem) {
                const fileId = fileItem.dataset.fileId;
                if (fileId) {
                    const favorites = JSON.parse(localStorage.getItem('fileShareFavorites') || '[]');
                    const isFavorited = favorites.some(fav => fav.id === fileId);
                    window.fileManager.updateFavoriteButtonState(fileId, isFavorited);
                }
            }
        });
        
        console.log('✅ 收藏状态刷新完成');
    }
    
    // 测试收藏功能
    function testFavoriteFunction() {
        console.log('🧪 测试收藏功能...');
        
        const favoriteButtons = document.querySelectorAll('[data-action="favorite"]');
        if (favoriteButtons.length === 0) {
            console.warn('⚠️ 没有找到收藏按钮');
            return;
        }
        
        const testBtn = favoriteButtons[0];
        const fileItem = testBtn.closest('[data-file-id]');
        const fileId = fileItem?.dataset.fileId;
        
        if (!fileId) {
            console.error('❌ 测试按钮没有文件ID');
            return;
        }
        
        console.log(`🎯 测试文件ID: ${fileId}`);
        
        // 模拟点击
        testBtn.click();
        console.log('✅ 测试点击已发送');
    }
    
    // 执行修复
    console.log('🚀 开始执行紧急修复...');
    rebindFavoriteEvents();
    refreshAllFavoriteStates();
    
    // 导出工具函数
    window.emergencyFavoriteTools = {
        rebind: rebindFavoriteEvents,
        refresh: refreshAllFavoriteStates,
        test: testFavoriteFunction,
        restore: () => {
            window.fileManager.favoriteFile = originalFavoriteFile;
            window.fileManager.updateFavoriteButtonState = originalUpdateFavoriteButtonState;
            console.log('🔄 已恢复原始方法');
        }
    };
    
    console.log('✅ 紧急修复完成！');
    console.log('💡 可用命令:');
    console.log('  emergencyFavoriteTools.test() - 测试收藏功能');
    console.log('  emergencyFavoriteTools.refresh() - 刷新收藏状态');
    console.log('  emergencyFavoriteTools.rebind() - 重新绑定事件');
    console.log('  emergencyFavoriteTools.restore() - 恢复原始方法');
    
})();
