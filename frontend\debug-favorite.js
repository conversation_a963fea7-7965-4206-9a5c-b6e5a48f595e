/**
 * 收藏功能调试脚本
 * 在浏览器控制台中运行此脚本来诊断收藏功能问题
 */

(function() {
    'use strict';
    
    console.log('=== 收藏功能调试开始 ===');
    
    // 1. 检查必要的全局对象
    function checkGlobalObjects() {
        console.log('\n1. 检查全局对象:');
        
        const requiredObjects = [
            'CONFIG',
            'Utils',
            'FileManager',
            'window.fileManager'
        ];
        
        requiredObjects.forEach(objName => {
            try {
                const obj = eval(objName);
                console.log(`✓ ${objName}:`, typeof obj, obj ? '存在' : '不存在');
            } catch (error) {
                console.log(`✗ ${objName}: 不存在 -`, error.message);
            }
        });
    }
    
    // 2. 检查localStorage权限
    function checkLocalStorage() {
        console.log('\n2. 检查localStorage:');
        
        try {
            const testKey = 'debug-test-' + Date.now();
            const testValue = { test: 'data' };
            
            localStorage.setItem(testKey, JSON.stringify(testValue));
            const retrieved = JSON.parse(localStorage.getItem(testKey));
            localStorage.removeItem(testKey);
            
            if (JSON.stringify(retrieved) === JSON.stringify(testValue)) {
                console.log('✓ localStorage 读写正常');
            } else {
                console.log('✗ localStorage 数据不匹配');
            }
        } catch (error) {
            console.log('✗ localStorage 错误:', error.message);
        }
    }
    
    // 3. 检查当前收藏数据
    function checkCurrentFavorites() {
        console.log('\n3. 检查当前收藏数据:');
        
        try {
            const favoritesData = localStorage.getItem('fileShareFavorites');
            if (favoritesData) {
                const favorites = JSON.parse(favoritesData);
                console.log(`✓ 找到 ${favorites.length} 个收藏项:`, favorites);
            } else {
                console.log('ℹ 没有收藏数据');
            }
        } catch (error) {
            console.log('✗ 收藏数据解析错误:', error.message);
        }
    }
    
    // 4. 检查文件列表
    function checkFileList() {
        console.log('\n4. 检查文件列表:');
        
        if (window.fileManager && window.fileManager.files) {
            console.log(`✓ 找到 ${window.fileManager.files.length} 个文件:`, window.fileManager.files);
        } else {
            console.log('✗ 未找到文件列表');
        }
    }
    
    // 5. 检查DOM元素
    function checkDOMElements() {
        console.log('\n5. 检查DOM元素:');
        
        const fileItems = document.querySelectorAll('.file-item');
        console.log(`✓ 找到 ${fileItems.length} 个文件项`);
        
        const favoriteButtons = document.querySelectorAll('[data-action="favorite"]');
        console.log(`✓ 找到 ${favoriteButtons.length} 个收藏按钮`);
        
        // 检查第一个收藏按钮的详细信息
        if (favoriteButtons.length > 0) {
            const firstBtn = favoriteButtons[0];
            const fileItem = firstBtn.closest('.file-item');
            const fileId = fileItem?.dataset.fileId;
            
            console.log('第一个收藏按钮详情:');
            console.log('- 按钮元素:', firstBtn);
            console.log('- 文件项:', fileItem);
            console.log('- 文件ID:', fileId);
            console.log('- 图标元素:', firstBtn.querySelector('i'));
        }
    }
    
    // 6. 测试收藏功能
    function testFavoriteFunction() {
        console.log('\n6. 测试收藏功能:');
        
        if (!window.fileManager) {
            console.log('✗ fileManager 不存在，无法测试');
            return;
        }
        
        if (typeof window.fileManager.favoriteFile !== 'function') {
            console.log('✗ favoriteFile 方法不存在');
            return;
        }
        
        // 查找第一个文件进行测试
        const fileItems = document.querySelectorAll('.file-item[data-file-id]');
        if (fileItems.length === 0) {
            console.log('✗ 没有找到可测试的文件');
            return;
        }
        
        const testFileId = fileItems[0].dataset.fileId;
        console.log(`ℹ 准备测试文件ID: ${testFileId}`);
        
        // 模拟收藏操作
        try {
            console.log('ℹ 执行收藏操作...');
            window.fileManager.favoriteFile(testFileId);
            console.log('✓ 收藏操作已执行（检查控制台日志）');
        } catch (error) {
            console.log('✗ 收藏操作失败:', error.message);
        }
    }
    
    // 7. 检查事件绑定
    function checkEventBinding() {
        console.log('\n7. 检查事件绑定:');
        
        // 创建一个测试点击事件
        const favoriteButtons = document.querySelectorAll('[data-action="favorite"]');
        if (favoriteButtons.length > 0) {
            const testBtn = favoriteButtons[0];
            
            console.log('ℹ 模拟点击收藏按钮...');
            
            // 创建点击事件
            const clickEvent = new MouseEvent('click', {
                bubbles: true,
                cancelable: true,
                view: window
            });
            
            // 监听事件
            let eventCaught = false;
            const eventListener = (e) => {
                if (e.target.closest('.action-btn')) {
                    eventCaught = true;
                    console.log('✓ 点击事件被捕获');
                }
            };
            
            document.addEventListener('click', eventListener, true);
            
            // 触发事件
            testBtn.dispatchEvent(clickEvent);
            
            setTimeout(() => {
                document.removeEventListener('click', eventListener, true);
                if (!eventCaught) {
                    console.log('✗ 点击事件未被捕获');
                }
            }, 100);
        } else {
            console.log('✗ 没有收藏按钮可测试');
        }
    }
    
    // 8. 提供修复建议
    function provideSuggestions() {
        console.log('\n8. 修复建议:');
        
        const suggestions = [
            '1. 刷新页面并清除浏览器缓存 (Ctrl+F5)',
            '2. 检查浏览器控制台是否有JavaScript错误',
            '3. 确认所有JavaScript文件都已正确加载',
            '4. 检查localStorage是否被浏览器策略阻止',
            '5. 确认文件管理器已正确初始化'
        ];
        
        suggestions.forEach(suggestion => {
            console.log(suggestion);
        });
    }
    
    // 执行所有检查
    function runAllChecks() {
        checkGlobalObjects();
        checkLocalStorage();
        checkCurrentFavorites();
        checkFileList();
        checkDOMElements();
        checkEventBinding();
        testFavoriteFunction();
        provideSuggestions();
        
        console.log('\n=== 收藏功能调试完成 ===');
    }
    
    // 导出调试函数到全局
    window.debugFavorite = {
        runAll: runAllChecks,
        checkGlobals: checkGlobalObjects,
        checkStorage: checkLocalStorage,
        checkFavorites: checkCurrentFavorites,
        checkFiles: checkFileList,
        checkDOM: checkDOMElements,
        checkEvents: checkEventBinding,
        testFunction: testFavoriteFunction,
        suggestions: provideSuggestions
    };
    
    console.log('调试工具已加载。运行 debugFavorite.runAll() 开始完整诊断');
    
})();
