<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>收藏功能修复工具</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .step {
            margin-bottom: 25px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 6px;
            background: #fafafa;
        }
        .step h3 {
            margin-top: 0;
            color: #2c3e50;
        }
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 10px 0;
        }
        .button {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
        }
        .button:hover {
            background: #2980b9;
        }
        .button.success {
            background: #27ae60;
        }
        .button.warning {
            background: #f39c12;
        }
        .button.danger {
            background: #e74c3c;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 收藏功能修复工具</h1>
        
        <div class="step">
            <h3>步骤 1: 复制修复脚本</h3>
            <p>点击下面的按钮复制修复脚本到剪贴板：</p>
            <button class="button" onclick="copyFixScript()">📋 复制修复脚本</button>
            <div id="copy-status"></div>
        </div>
        
        <div class="step">
            <h3>步骤 2: 在正式系统中执行</h3>
            <p>1. 打开您的文件共享系统页面</p>
            <p>2. 按 F12 打开开发者工具</p>
            <p>3. 切换到 "Console" (控制台) 标签</p>
            <p>4. 粘贴刚才复制的脚本并按回车执行</p>
        </div>
        
        <div class="step">
            <h3>步骤 3: 使用修复工具</h3>
            <p>脚本执行后，您可以使用以下命令：</p>
            <div class="code-block">
favoriteTools.refresh()  // 刷新收藏状态
favoriteTools.test()     // 测试收藏功能
favoriteTools.show()     // 显示收藏列表
favoriteTools.clear()    // 清空收藏列表（测试用）
            </div>
        </div>
        
        <div class="step">
            <h3>步骤 4: 验证修复效果</h3>
            <p>1. 尝试点击任意文件的收藏按钮（星星图标）</p>
            <p>2. 观察星星是否从空心变为实心（或相反）</p>
            <p>3. 检查浏览器控制台是否有详细的操作日志</p>
            <p>4. 刷新页面后检查收藏状态是否保持</p>
        </div>
        
        <div class="step">
            <h3>🚨 如果问题仍然存在</h3>
            <p>请在浏览器控制台中运行以下诊断命令：</p>
            <div class="code-block">
console.log('FileManager:', window.fileManager);
console.log('CONFIG:', window.CONFIG);
console.log('收藏按钮数量:', document.querySelectorAll('[data-action="favorite"]').length);
console.log('localStorage 测试:', (() => {
    try {
        localStorage.setItem('test', 'ok');
        const result = localStorage.getItem('test');
        localStorage.removeItem('test');
        return result === 'ok' ? '正常' : '异常';
    } catch(e) {
        return '错误: ' + e.message;
    }
})());
            </div>
            <p>然后将控制台输出截图发送给开发者。</p>
        </div>
    </div>

    <script>
        const fixScript = `(function() {
    'use strict';
    
    console.log('🔧 开始修复收藏功能...');
    
    // 检查必要组件
    if (!window.fileManager) {
        console.error('❌ fileManager 不存在');
        return;
    }
    
    if (!window.CONFIG) {
        console.error('❌ CONFIG 不存在');
        return;
    }
    
    console.log('✅ 基础组件检查通过');
    
    // 强制刷新收藏状态
    function forceRefreshFavorites() {
        console.log('🔄 强制刷新收藏状态...');
        
        const favoriteButtons = document.querySelectorAll('[data-action="favorite"]');
        console.log(\`找到 \${favoriteButtons.length} 个收藏按钮\`);
        
        favoriteButtons.forEach((btn, index) => {
            const fileItem = btn.closest('.file-item');
            if (fileItem) {
                const fileId = fileItem.dataset.fileId;
                if (fileId) {
                    const favorites = JSON.parse(localStorage.getItem('fileShareFavorites') || '[]');
                    const isFavorited = favorites.some(fav => fav.id === fileId);
                    
                    const icon = btn.querySelector('i');
                    if (icon) {
                        if (isFavorited) {
                            icon.className = 'fas fa-star';
                            btn.classList.add('favorited');
                            btn.title = '取消收藏';
                        } else {
                            icon.className = 'far fa-star';
                            btn.classList.remove('favorited');
                            btn.title = '收藏';
                        }
                        console.log(\`按钮 \${index + 1}: \${fileId} -> \${isFavorited}\`);
                    }
                }
            }
        });
        
        console.log('✅ 收藏状态刷新完成');
    }
    
    // 修复事件绑定
    function fixEventBinding() {
        console.log('🔧 修复事件绑定...');
        
        document.removeEventListener('click', window.favoriteClickHandler);
        
        window.favoriteClickHandler = function(e) {
            const actionBtn = e.target.closest('.action-btn');
            if (!actionBtn) return;
            
            const action = actionBtn.dataset.action;
            if (action !== 'favorite') return;
            
            e.preventDefault();
            e.stopPropagation();
            
            const fileItem = actionBtn.closest('.file-item');
            const fileId = fileItem?.dataset.fileId;
            
            console.log(\`收藏按钮点击: \${fileId}\`);
            
            if (!fileId) {
                console.error('未找到文件ID');
                return;
            }
            
            if (window.fileManager && typeof window.fileManager.favoriteFile === 'function') {
                window.fileManager.favoriteFile(fileId);
            } else {
                console.error('favoriteFile 方法不存在');
            }
        };
        
        document.addEventListener('click', window.favoriteClickHandler);
        console.log('✅ 事件绑定修复完成');
    }
    
    // 测试收藏功能
    function testFavoriteFunction() {
        console.log('🧪 测试收藏功能...');
        
        const favoriteButtons = document.querySelectorAll('[data-action="favorite"]');
        if (favoriteButtons.length === 0) {
            console.warn('⚠️ 没有找到收藏按钮');
            return;
        }
        
        const testBtn = favoriteButtons[0];
        const fileItem = testBtn.closest('.file-item');
        const fileId = fileItem?.dataset.fileId;
        
        if (!fileId) {
            console.error('❌ 测试按钮没有文件ID');
            return;
        }
        
        console.log(\`测试文件ID: \${fileId}\`);
        
        const clickEvent = new MouseEvent('click', {
            bubbles: true,
            cancelable: true,
            view: window
        });
        
        testBtn.dispatchEvent(clickEvent);
        console.log('✅ 测试点击已发送');
    }
    
    // 显示当前收藏列表
    function showCurrentFavorites() {
        console.log('📋 当前收藏列表:');
        
        try {
            const favorites = JSON.parse(localStorage.getItem('fileShareFavorites') || '[]');
            console.table(favorites);
            console.log(\`总计: \${favorites.length} 个收藏项\`);
        } catch (error) {
            console.error('❌ 读取收藏列表失败:', error);
        }
    }
    
    // 清空收藏列表
    function clearFavorites() {
        localStorage.removeItem('fileShareFavorites');
        console.log('🗑️ 收藏列表已清空');
        forceRefreshFavorites();
    }
    
    // 执行修复
    console.log('🚀 开始执行修复步骤...');
    forceRefreshFavorites();
    fixEventBinding();
    showCurrentFavorites();
    
    // 导出工具函数
    window.favoriteTools = {
        refresh: forceRefreshFavorites,
        fixEvents: fixEventBinding,
        test: testFavoriteFunction,
        show: showCurrentFavorites,
        clear: clearFavorites
    };
    
    console.log('✅ 收藏功能修复完成！');
    console.log('💡 可用命令:');
    console.log('  favoriteTools.refresh() - 刷新收藏状态');
    console.log('  favoriteTools.test() - 测试收藏功能');
    console.log('  favoriteTools.show() - 显示收藏列表');
    console.log('  favoriteTools.clear() - 清空收藏列表');
    
})();`;

        function copyFixScript() {
            navigator.clipboard.writeText(fixScript).then(() => {
                const status = document.getElementById('copy-status');
                status.innerHTML = '<div class="status success">✅ 修复脚本已复制到剪贴板！</div>';
                setTimeout(() => {
                    status.innerHTML = '';
                }, 3000);
            }).catch(err => {
                const status = document.getElementById('copy-status');
                status.innerHTML = '<div class="status error">❌ 复制失败，请手动复制下面的代码</div>';
                
                // 显示代码块供手动复制
                const codeBlock = document.createElement('div');
                codeBlock.className = 'code-block';
                codeBlock.style.marginTop = '10px';
                codeBlock.style.maxHeight = '200px';
                codeBlock.style.overflow = 'auto';
                codeBlock.textContent = fixScript;
                status.appendChild(codeBlock);
            });
        }
    </script>
</body>
</html>
